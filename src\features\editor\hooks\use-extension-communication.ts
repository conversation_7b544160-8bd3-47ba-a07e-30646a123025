import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  ExtensionRecordingData, 
  ExtensionMessage, 
  EditorMessage,
  URLParams,
  URLParamHandler,
  ExtensionMessageHandler 
} from '../../../types/extension-communication';

export interface ExtensionCommunicationState {
  isFromExtension: boolean;
  recordingData: ExtensionRecordingData | null;
  isLoading: boolean;
  error: string | null;
  urlParams: URLParams;
}

export interface ExtensionCommunicationActions {
  loadRecordingFromParams: () => Promise<void>;
  handleExtensionMessage: (message: ExtensionMessage) => void;
  sendReadyMessage: () => void;
  sendLoadedMessage: () => void;
  sendErrorMessage: (error: string) => void;
  clearData: () => void;
}

export function useExtensionCommunication() {
  const [state, setState] = useState<ExtensionCommunicationState>({
    isFromExtension: false,
    recordingData: null,
    isLoading: false,
    error: null,
    urlParams: {},
  });

  const messageHandlerRef = useRef<ExtensionMessageHandler | null>(null);

  // Initialize message handler and parse URL params
  useEffect(() => {
    const urlParams = URLParamHandler.parseParams();
    const isFromExtension = urlParams.fromExtension === 'true';

    setState(prev => ({
      ...prev,
      isFromExtension,
      urlParams,
    }));

    if (isFromExtension) {
      // Initialize message handler for extension communication
      messageHandlerRef.current = new ExtensionMessageHandler();
      messageHandlerRef.current.addListener('main', handleExtensionMessage);
      
      // Send ready message to extension
      sendReadyMessage();
    }

    return () => {
      if (messageHandlerRef.current) {
        messageHandlerRef.current.cleanup();
        messageHandlerRef.current = null;
      }
    };
  }, []);

  const handleExtensionMessage = useCallback((message: ExtensionMessage) => {
    console.log('Received extension message:', message);

    switch (message.type) {
      case 'RECORDING_COMPLETE':
      case 'RECORDING_DATA':
        if (message.data) {
          setState(prev => ({
            ...prev,
            recordingData: message.data!,
            isLoading: false,
            error: null,
          }));
        }
        break;
      
      case 'PING':
        // Respond to ping
        sendReadyMessage();
        break;
      
      default:
        console.warn('Unknown extension message type:', message.type);
    }
  }, []);

  const loadRecordingFromParams = useCallback(async (): Promise<void> => {
    const { urlParams } = state;
    
    if (!urlParams.videoUrl) {
      setState(prev => ({ ...prev, error: 'No video URL provided' }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Parse mouse data if provided
      let mouseActions = [];
      if (urlParams.mouseData) {
        try {
          const parsedMouseData = JSON.parse(urlParams.mouseData);
          mouseActions = parsedMouseData.mouseActions || parsedMouseData || [];
        } catch (error) {
          console.warn('Failed to parse mouse data:', error);
        }
      }

      // Create recording data from URL parameters
      const recordingData: ExtensionRecordingData = {
        videoUrl: urlParams.videoUrl,
        videoDuration: parseInt(urlParams.videoDuration || '0'),
        videoWidth: parseInt(urlParams.videoWidth || '1920'),
        videoHeight: parseInt(urlParams.videoHeight || '1080'),
        mouseActions,
        recordingInfo: {
          totalCoordinates: mouseActions.filter(a => a.action === 'move').length,
          totalClicks: mouseActions.filter(a => a.action === 'click').length,
          timestamp: urlParams.timestamp || new Date().toISOString(),
          tabInfo: urlParams.tabTitle ? {
            title: urlParams.tabTitle,
            url: urlParams.tabUrl || '',
            width: parseInt(urlParams.tabWidth || '1920'),
            height: parseInt(urlParams.tabHeight || '1080'),
          } : undefined,
        },
      };

      setState(prev => ({
        ...prev,
        recordingData,
        isLoading: false,
        error: null,
      }));

      // Clear URL parameters after loading
      URLParamHandler.clearParams();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load recording data';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
    }
  }, [state.urlParams]);

  const sendReadyMessage = useCallback(() => {
    if (messageHandlerRef.current) {
      const message: EditorMessage = {
        type: 'READY',
        source: 'editor',
      };
      messageHandlerRef.current.sendMessage(message);
    }
  }, []);

  const sendLoadedMessage = useCallback(() => {
    if (messageHandlerRef.current) {
      const message: EditorMessage = {
        type: 'LOADED',
        source: 'editor',
      };
      messageHandlerRef.current.sendMessage(message);
    }
  }, []);

  const sendErrorMessage = useCallback((error: string) => {
    if (messageHandlerRef.current) {
      const message: EditorMessage = {
        type: 'ERROR',
        data: { error },
        source: 'editor',
      };
      messageHandlerRef.current.sendMessage(message);
    }
  }, []);

  const clearData = useCallback(() => {
    setState(prev => ({
      ...prev,
      recordingData: null,
      error: null,
      isLoading: false,
    }));
  }, []);

  const actions: ExtensionCommunicationActions = {
    loadRecordingFromParams,
    handleExtensionMessage,
    sendReadyMessage,
    sendLoadedMessage,
    sendErrorMessage,
    clearData,
  };

  return {
    ...state,
    actions,
  };
}
