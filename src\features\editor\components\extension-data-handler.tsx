import React, { useEffect, useState } from 'react';
import { useExtensionCommunication } from '../hooks/use-extension-communication';
import { useLocalVideosStore } from '../store/use-local-videos-store';
import { useMouseTrackingStore, MouseTrackingUtils } from '../store/use-mouse-tracking-store';
import { dispatch } from '@designcombo/events';
import { ADD_VIDEO } from '@designcombo/state';
import { generateId } from '@designcombo/timeline';
import { IVideo } from '@designcombo/types';
import { ExtensionRecordingData } from '../../../types/extension-communication';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { AlertCircle, CheckCircle, Monitor, Mouse } from 'lucide-react';

interface ExtensionDataHandlerProps {
  onDataLoaded?: (data: ExtensionRecordingData) => void;
  onError?: (error: string) => void;
}

export function ExtensionDataHandler({ onDataLoaded, onError }: ExtensionDataHandlerProps) {
  const {
    isFromExtension,
    recordingData,
    isLoading,
    error,
    urlParams,
    actions,
  } = useExtensionCommunication();

  const { actions: videoActions } = useLocalVideosStore();
  const { actions: mouseTrackingActions } = useMouseTrackingStore();
  const [processingState, setProcessingState] = useState<'idle' | 'loading-video' | 'processing-mouse-data' | 'adding-to-timeline' | 'complete'>('idle');
  const [processingError, setProcessingError] = useState<string | null>(null);

  // Load data from URL parameters on mount if coming from extension
  useEffect(() => {
    if (isFromExtension && urlParams.videoUrl && !recordingData) {
      actions.loadRecordingFromParams();
    }
  }, [isFromExtension, urlParams.videoUrl, recordingData, actions]);

  // Process recording data when it becomes available
  useEffect(() => {
    if (recordingData && processingState === 'idle') {
      processRecordingData(recordingData);
    }
  }, [recordingData, processingState]);

  // Handle errors
  useEffect(() => {
    if (error) {
      setProcessingError(error);
      onError?.(error);
      actions.sendErrorMessage(error);
    }
  }, [error, onError, actions]);

  const processRecordingData = async (data: ExtensionRecordingData) => {
    try {
      setProcessingState('loading-video');
      setProcessingError(null);

      // Create video file from URL or blob
      let videoFile: File;
      
      if (data.videoBlob) {
        // Use blob directly
        videoFile = new File([data.videoBlob], 'extension-recording.webm', { type: 'video/webm' });
      } else if (data.videoUrl) {
        // Fetch video from URL
        const response = await fetch(data.videoUrl);
        if (!response.ok) {
          throw new Error(`Failed to fetch video: ${response.statusText}`);
        }
        const blob = await response.blob();
        videoFile = new File([blob], 'extension-recording.webm', { type: blob.type || 'video/webm' });
      } else {
        throw new Error('No video data provided');
      }

      // Add video to local store
      const localVideo = await videoActions.addVideo(videoFile);

      setProcessingState('processing-mouse-data');

      // Process mouse tracking data if available
      if (data.mouseActions && data.mouseActions.length > 0) {
        const trackingData = MouseTrackingUtils.fromExtensionData(
          data.mouseActions,
          localVideo.width,
          localVideo.height,
          data.recordingInfo
        );
        mouseTrackingActions.setTrackingData(trackingData);
        console.log('Mouse tracking data processed:', trackingData);
      }

      setProcessingState('adding-to-timeline');

      // Create video data for timeline
      const videoData: Partial<IVideo> = {
        id: generateId(),
        details: {
          src: localVideo.objectUrl,
          width: localVideo.width,
          height: localVideo.height,
          blur: 0,
          brightness: 100,
          flipX: false,
          flipY: false,
          rotate: "0",
          visibility: "visible",
        },
        type: "video",
        metadata: {
          previewUrl: localVideo.thumbnailUrl || localVideo.objectUrl,
          localVideoId: localVideo.id,
          fileName: localVideo.name,
          // Store extension data for later use
          extensionData: {
            mouseActions: data.mouseActions,
            recordingInfo: data.recordingInfo,
          },
        },
        duration: localVideo.duration,
      };

      // Add to timeline
      dispatch(ADD_VIDEO, {
        payload: videoData,
        options: {
          resourceId: "main",
          scaleMode: "fit",
        },
      });

      setProcessingState('complete');
      
      // Notify callbacks
      onDataLoaded?.(data);
      actions.sendLoadedMessage();

      console.log('Extension recording data processed successfully:', {
        video: {
          duration: localVideo.duration,
          width: localVideo.width,
          height: localVideo.height,
        },
        mouseActions: data.mouseActions.length,
        clicks: data.recordingInfo.totalClicks,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process recording data';
      setProcessingError(errorMessage);
      setProcessingState('idle');
      onError?.(errorMessage);
      actions.sendErrorMessage(errorMessage);
    }
  };

  // Don't render anything if not from extension
  if (!isFromExtension) {
    return null;
  }

  // Show loading state
  if (isLoading || processingState !== 'idle' && processingState !== 'complete') {
    return (
      <Card className="w-full max-w-md mx-auto mt-8">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            <Monitor className="h-5 w-5" />
            Loading Extension Recording
          </CardTitle>
          <CardDescription>
            Processing video and mouse tracking data from browser extension
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>
                {processingState === 'loading-video' && 'Loading video...'}
                {processingState === 'processing-mouse-data' && 'Processing mouse data...'}
                {processingState === 'adding-to-timeline' && 'Adding to timeline...'}
              </span>
            </div>
            <Progress value={
              processingState === 'loading-video' ? 33 :
              processingState === 'processing-mouse-data' ? 66 : 90
            } />
          </div>
          
          {recordingData && (
            <div className="text-sm text-muted-foreground space-y-1">
              <p>Video: {recordingData.videoWidth}×{recordingData.videoHeight}</p>
              <p>Duration: {(recordingData.videoDuration / 1000).toFixed(1)}s</p>
              <p className="flex items-center gap-1">
                <Mouse className="h-3 w-3" />
                {recordingData.recordingInfo.totalCoordinates} mouse movements, {recordingData.recordingInfo.totalClicks} clicks
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Show error state
  if (error || processingError) {
    return (
      <Card className="w-full max-w-md mx-auto mt-8 border-destructive">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            Failed to Load Recording
          </CardTitle>
          <CardDescription>
            There was an error processing the extension recording data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-destructive">
            {error || processingError}
          </p>
        </CardContent>
      </Card>
    );
  }

  // Show success state briefly
  if (processingState === 'complete') {
    return (
      <Card className="w-full max-w-md mx-auto mt-8 border-green-500">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2 text-green-600">
            <CheckCircle className="h-5 w-5" />
            Recording Loaded Successfully
          </CardTitle>
          <CardDescription>
            Your extension recording is ready for editing
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return null;
}
