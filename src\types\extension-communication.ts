/**
 * Types for communication between browser extension and react-video-editor
 */

export interface MouseAction {
  action: 'move' | 'click';
  coords: {
    x: number;
    y: number;
  };
  timestamp?: number;
}

export interface ExtensionRecordingData {
  // Video data
  videoBlob?: Blob;
  videoUrl?: string;
  videoDuration: number;
  videoWidth: number;
  videoHeight: number;
  
  // Mouse tracking data
  mouseActions: MouseAction[];
  recordingInfo: {
    totalCoordinates: number;
    totalClicks: number;
    timestamp: string;
    tabInfo?: {
      title: string;
      url: string;
      width: number;
      height: number;
    };
  };
}

export interface ExtensionMessage {
  type: 'RECORDING_COMPLETE' | 'RECORDING_DATA' | 'PING';
  data?: ExtensionRecordingData;
  source: 'extension';
}

export interface EditorMessage {
  type: 'READY' | 'LOADED' | 'ERROR';
  data?: any;
  source: 'editor';
}

export interface URLParams {
  // Extension communication
  fromExtension?: 'true';
  recordingId?: string;
  
  // Video data (if passed via URL)
  videoUrl?: string;
  videoDuration?: string;
  videoWidth?: string;
  videoHeight?: string;
  
  // Mouse data (if passed via URL - though this might be too large)
  mouseData?: string; // JSON encoded mouse actions
  
  // Recording metadata
  tabTitle?: string;
  tabUrl?: string;
  tabWidth?: string;
  tabHeight?: string;
  timestamp?: string;
}

/**
 * Utility functions for URL parameter handling
 */
export class URLParamHandler {
  static parseParams(): URLParams {
    const urlParams = new URLSearchParams(window.location.search);
    const params: URLParams = {};
    
    // Parse boolean flags
    if (urlParams.get('fromExtension') === 'true') {
      params.fromExtension = 'true';
    }
    
    // Parse string parameters
    const stringParams = ['recordingId', 'videoUrl', 'tabTitle', 'tabUrl', 'timestamp'];
    stringParams.forEach(param => {
      const value = urlParams.get(param);
      if (value) {
        (params as any)[param] = decodeURIComponent(value);
      }
    });
    
    // Parse numeric parameters
    const numericParams = ['videoDuration', 'videoWidth', 'videoHeight', 'tabWidth', 'tabHeight'];
    numericParams.forEach(param => {
      const value = urlParams.get(param);
      if (value && !isNaN(Number(value))) {
        (params as any)[param] = value;
      }
    });
    
    // Parse JSON parameters
    const mouseDataParam = urlParams.get('mouseData');
    if (mouseDataParam) {
      try {
        params.mouseData = decodeURIComponent(mouseDataParam);
      } catch (error) {
        console.warn('Failed to parse mouseData parameter:', error);
      }
    }
    
    return params;
  }
  
  static buildURL(baseUrl: string, params: URLParams): string {
    const url = new URL(baseUrl);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (key === 'mouseData') {
          // Encode JSON data
          url.searchParams.set(key, encodeURIComponent(value));
        } else {
          url.searchParams.set(key, String(value));
        }
      }
    });
    
    return url.toString();
  }
  
  static clearParams(): void {
    const url = new URL(window.location.href);
    url.search = '';
    window.history.replaceState({}, document.title, url.toString());
  }
}

/**
 * Message passing utilities for extension communication
 */
export class ExtensionMessageHandler {
  private listeners: Map<string, (message: ExtensionMessage) => void> = new Map();
  
  constructor() {
    // Listen for messages from extension
    window.addEventListener('message', this.handleMessage.bind(this));
  }
  
  private handleMessage(event: MessageEvent) {
    // Verify origin for security (adjust as needed)
    if (event.origin !== window.location.origin) {
      return;
    }
    
    const message = event.data as ExtensionMessage;
    if (message.source === 'extension') {
      this.notifyListeners(message);
    }
  }
  
  private notifyListeners(message: ExtensionMessage) {
    this.listeners.forEach(listener => {
      try {
        listener(message);
      } catch (error) {
        console.error('Error in message listener:', error);
      }
    });
  }
  
  addListener(id: string, listener: (message: ExtensionMessage) => void) {
    this.listeners.set(id, listener);
  }
  
  removeListener(id: string) {
    this.listeners.delete(id);
  }
  
  sendMessage(message: EditorMessage) {
    // Send message back to extension
    window.postMessage(message, window.location.origin);
  }
  
  cleanup() {
    this.listeners.clear();
    window.removeEventListener('message', this.handleMessage.bind(this));
  }
}
