import React, { useEffect, useRef, useState } from 'react';
import { useMouseTrackingStore, MouseTrackingPoint, MouseTrackingUtils } from '../store/use-mouse-tracking-store';
import useStore from '../store/use-store';

interface MouseTrackingOverlayProps {
  width: number;
  height: number;
  currentTime: number; // in milliseconds
  className?: string;
}

export function MouseTrackingOverlay({ width, height, currentTime, className }: MouseTrackingOverlayProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const { trackingData, visualization } = useMouseTrackingStore();
  const [isVisible, setIsVisible] = useState(true);

  // Draw the mouse tracking visualization
  useEffect(() => {
    if (!canvasRef.current || !trackingData || !isVisible) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Set up canvas properties
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Get points up to current time
    const currentPoints = trackingData.points.filter(point => point.timestamp <= currentTime);
    
    if (currentPoints.length === 0) return;

    // Draw mouse path
    if (visualization.showPath && currentPoints.length > 1) {
      drawMousePath(ctx, currentPoints, width, height);
    }

    // Draw clicks
    if (visualization.showClicks) {
      drawClicks(ctx, currentPoints, width, height);
    }

    // Draw current mouse position
    drawCurrentPosition(ctx, currentPoints, currentTime, width, height);

  }, [trackingData, currentTime, width, height, visualization, isVisible]);

  const drawMousePath = (
    ctx: CanvasRenderingContext2D,
    points: MouseTrackingPoint[],
    canvasWidth: number,
    canvasHeight: number
  ) => {
    const movePoints = points.filter(p => p.action === 'move');
    if (movePoints.length < 2) return;

    ctx.strokeStyle = visualization.pathColor;
    ctx.globalAlpha = visualization.opacity;
    ctx.lineWidth = visualization.pathWidth;

    ctx.beginPath();
    
    // Convert normalized coordinates to canvas coordinates
    const firstPoint = movePoints[0];
    ctx.moveTo(
      firstPoint.normalizedX * canvasWidth,
      firstPoint.normalizedY * canvasHeight
    );

    for (let i = 1; i < movePoints.length; i++) {
      const point = movePoints[i];
      ctx.lineTo(
        point.normalizedX * canvasWidth,
        point.normalizedY * canvasHeight
      );
    }

    ctx.stroke();
    ctx.globalAlpha = 1;
  };

  const drawClicks = (
    ctx: CanvasRenderingContext2D,
    points: MouseTrackingPoint[],
    canvasWidth: number,
    canvasHeight: number
  ) => {
    const clickPoints = points.filter(p => p.action === 'click');
    
    ctx.fillStyle = visualization.clickColor;
    ctx.globalAlpha = visualization.opacity;

    clickPoints.forEach(point => {
      const x = point.normalizedX * canvasWidth;
      const y = point.normalizedY * canvasHeight;

      // Draw click indicator (circle with pulse effect)
      ctx.beginPath();
      ctx.arc(x, y, visualization.clickRadius, 0, 2 * Math.PI);
      ctx.fill();

      // Draw inner circle for contrast
      ctx.fillStyle = '#ffffff';
      ctx.beginPath();
      ctx.arc(x, y, visualization.clickRadius * 0.4, 0, 2 * Math.PI);
      ctx.fill();
      
      // Reset fill style
      ctx.fillStyle = visualization.clickColor;
    });

    ctx.globalAlpha = 1;
  };

  const drawCurrentPosition = (
    ctx: CanvasRenderingContext2D,
    points: MouseTrackingPoint[],
    time: number,
    canvasWidth: number,
    canvasHeight: number
  ) => {
    // Get interpolated position at current time
    const position = MouseTrackingUtils.getInterpolatedPosition(points, time);
    if (!position) return;

    const x = position.normalizedX * canvasWidth;
    const y = position.normalizedY * canvasHeight;

    // Draw current mouse cursor
    ctx.fillStyle = '#ffffff';
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.globalAlpha = 0.9;

    // Draw cursor shape (arrow-like)
    ctx.beginPath();
    ctx.moveTo(x, y);
    ctx.lineTo(x + 12, y + 12);
    ctx.lineTo(x + 8, y + 16);
    ctx.lineTo(x + 4, y + 12);
    ctx.closePath();
    ctx.fill();
    ctx.stroke();

    ctx.globalAlpha = 1;
  };

  // Toggle visibility with keyboard shortcut
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.key === 'm') {
        e.preventDefault();
        setIsVisible(!isVisible);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isVisible]);

  if (!trackingData) return null;

  return (
    <div className={`absolute inset-0 pointer-events-none ${className}`}>
      <canvas
        ref={canvasRef}
        width={width}
        height={height}
        className={`absolute inset-0 transition-opacity duration-300 ${
          isVisible ? 'opacity-100' : 'opacity-0'
        }`}
        style={{ width: '100%', height: '100%' }}
      />
      
      {/* Toggle indicator */}
      <div className="absolute top-2 right-2 text-xs text-white bg-black bg-opacity-50 px-2 py-1 rounded">
        Mouse Tracking: {isVisible ? 'ON' : 'OFF'} (Ctrl+M)
      </div>
    </div>
  );
}

/**
 * Hook to sync mouse tracking with video playback
 */
export function useMouseTrackingSync() {
  const { playerRef } = useStore();
  const { actions } = useMouseTrackingStore();

  useEffect(() => {
    if (!playerRef?.current) return;

    const player = playerRef.current;

    const handleTimeUpdate = () => {
      const currentTime = player.getCurrentTime() * 1000; // Convert to milliseconds
      actions.setCurrentTime(currentTime);
    };

    const handlePlay = () => {
      actions.setIsPlaying(true);
    };

    const handlePause = () => {
      actions.setIsPlaying(false);
    };

    // Listen to player events
    player.addEventListener('timeupdate', handleTimeUpdate);
    player.addEventListener('play', handlePlay);
    player.addEventListener('pause', handlePause);

    return () => {
      player.removeEventListener('timeupdate', handleTimeUpdate);
      player.removeEventListener('play', handlePlay);
      player.removeEventListener('pause', handlePause);
    };
  }, [playerRef, actions]);
}
