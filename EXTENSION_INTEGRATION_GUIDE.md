# Browser Extension Integration Guide

This guide explains how to test and use the integrated browser extension with the react-video-editor.

## Overview

The integration allows users to:
1. Record screen/tab/window with mouse tracking using the browser extension
2. Automatically open the react-video-editor with the recorded video and mouse data
3. Edit the video with mouse tracking visualization and effects

## Setup Instructions

### 1. Install the Browser Extension

1. Open Chrome and navigate to `chrome://extensions/`
2. Enable "Developer mode" in the top right
3. Click "Load unpacked" and select the extension directory
4. The "Mouse Tracker & Screen Recorder" extension should appear

### 2. Start the React Video Editor

1. Navigate to the react-video-editor directory
2. Install dependencies: `npm install`
3. Start the development server: `npm run dev`
4. The editor should be running at `http://localhost:5173`

### 3. Update Extension Configuration

If your video editor is running on a different port, update the `editorBaseUrl` in `control.js`:

```javascript
const editorBaseUrl = 'http://localhost:5173'; // Change this if needed
```

## Testing the Integration

### Complete User Flow Test

1. **Open the Extension**
   - Click the extension icon in Chrome
   - The control page should open

2. **Prepare a Test Page**
   - Click "Open Test Page" to open a simple web page
   - Or navigate to any website you want to record

3. **Start Recording**
   - Select "Current Tab" recording type
   - Enable "Enable mouse tracking on selected tab"
   - Click "Start Recording"
   - The extension will switch to the target tab and start recording

4. **Perform Actions**
   - Move your mouse around the page
   - Click on various elements
   - Perform the actions you want to record

5. **Stop Recording**
   - Switch back to the extension control page
   - Click "Stop Recording"
   - The video editor should automatically open with your recording

6. **Verify Integration**
   - Check that the video loads in the timeline
   - Verify mouse tracking data is visible (press Ctrl+M to toggle)
   - Play the video to see mouse movements synchronized

### Manual Testing Options

#### Test with Manual Editor Opening
1. Record a video using the extension
2. Click "Open Video Editor" button manually
3. Verify the editor opens with the recording data

#### Test URL Parameter Handling
1. Record a video and note the generated URL
2. Copy the URL and open it in a new tab
3. Verify the editor loads the video and mouse data

#### Test Mouse Tracking Features
1. Load a recording with mouse data
2. Use Ctrl+M to toggle mouse tracking visibility
3. Use Ctrl+Shift+M to toggle the mouse tracking panel
4. Adjust visualization settings in the panel

## Features to Test

### Extension Features
- [x] Tab recording with mouse tracking
- [x] Window recording (mouse tracking disabled)
- [x] Screen recording (mouse tracking disabled)
- [x] Automatic video editor opening
- [x] Manual video editor opening
- [x] Mouse data visualization in extension
- [x] Download recording functionality

### Editor Features
- [x] URL parameter parsing
- [x] Extension data loading
- [x] Video timeline integration
- [x] Mouse tracking overlay
- [x] Mouse tracking control panel
- [x] Synchronized playback
- [x] Visualization customization

### Integration Features
- [x] Automatic editor launch after recording
- [x] Video and mouse data transfer
- [x] Real-time communication
- [x] Error handling and user feedback

## Troubleshooting

### Extension Issues

**Extension doesn't start recording:**
- Check that you're on a regular web page (not chrome:// or extension pages)
- Ensure the target tab is accessible
- Try refreshing the target tab

**Mouse tracking not working:**
- Verify the content script is injected
- Check browser console for errors
- Ensure the target tab allows script injection

### Editor Issues

**Editor doesn't open automatically:**
- Check that the editor is running at the correct URL
- Verify the `editorBaseUrl` in control.js
- Check browser popup blockers

**Video doesn't load:**
- Check browser console for errors
- Verify the video blob URL is accessible
- Ensure the video format is supported

**Mouse tracking not visible:**
- Press Ctrl+M to toggle visibility
- Check the mouse tracking panel settings
- Verify mouse data was captured during recording

### Common Issues

**CORS Errors:**
- Ensure the editor is running on localhost
- Check that blob URLs are accessible

**Performance Issues:**
- Large recordings may take time to process
- Consider shorter test recordings initially

**Browser Compatibility:**
- Chrome is recommended for best compatibility
- Firefox and Edge should also work

## Development Notes

### Key Files Modified

**React Video Editor:**
- `src/types/extension-communication.ts` - Communication types
- `src/features/editor/hooks/use-extension-communication.ts` - Extension communication hook
- `src/features/editor/components/extension-data-handler.tsx` - Data processing component
- `src/features/editor/store/use-mouse-tracking-store.ts` - Mouse tracking state management
- `src/features/editor/components/mouse-tracking-overlay.tsx` - Mouse visualization
- `src/features/editor/components/mouse-tracking-panel.tsx` - Control panel
- `src/features/editor/editor.tsx` - Main editor integration

**Browser Extension:**
- `control.js` - Added auto-launch functionality
- `control.html` - Added video editor button

### Data Flow

1. Extension captures video and mouse data
2. Extension creates blob URL and prepares data
3. Extension opens editor with URL parameters
4. Editor parses parameters and loads data
5. Editor processes mouse tracking data
6. Editor displays video with mouse overlay
7. User can control visualization and playback

## Next Steps

After successful testing, consider:
1. Publishing the browser extension
2. Adding more mouse tracking features
3. Implementing zoom effects based on mouse data
4. Adding export options for mouse tracking data
5. Creating preset mouse tracking visualizations
