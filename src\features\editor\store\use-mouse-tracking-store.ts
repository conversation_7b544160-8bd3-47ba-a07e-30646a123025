import { create } from "zustand";
import { MouseAction } from "../../../types/extension-communication";

export interface MouseTrackingPoint {
  x: number;
  y: number;
  timestamp: number;
  action: 'move' | 'click';
  // Normalized coordinates (0-1) for different screen sizes
  normalizedX: number;
  normalizedY: number;
}

export interface MouseTrackingData {
  id: string;
  points: MouseTrackingPoint[];
  recordingInfo: {
    totalCoordinates: number;
    totalClicks: number;
    timestamp: string;
    tabInfo?: {
      title: string;
      url: string;
      width: number;
      height: number;
    };
  };
  // Video dimensions for coordinate normalization
  videoWidth: number;
  videoHeight: number;
}

export interface MouseTrackingVisualization {
  showPath: boolean;
  showClicks: boolean;
  pathColor: string;
  clickColor: string;
  pathWidth: number;
  clickRadius: number;
  opacity: number;
}

interface MouseTrackingState {
  // Data
  trackingData: MouseTrackingData | null;
  
  // Visualization settings
  visualization: MouseTrackingVisualization;
  
  // Playback state
  currentTime: number; // in milliseconds
  isPlaying: boolean;
  
  // Actions
  actions: {
    setTrackingData: (data: MouseTrackingData) => void;
    clearTrackingData: () => void;
    updateVisualization: (settings: Partial<MouseTrackingVisualization>) => void;
    setCurrentTime: (time: number) => void;
    setIsPlaying: (playing: boolean) => void;
    getPointsAtTime: (time: number, tolerance?: number) => MouseTrackingPoint[];
    getClicksInRange: (startTime: number, endTime: number) => MouseTrackingPoint[];
    normalizeMouseActions: (actions: MouseAction[], videoWidth: number, videoHeight: number, tabWidth?: number, tabHeight?: number) => MouseTrackingPoint[];
  };
}

const DEFAULT_VISUALIZATION: MouseTrackingVisualization = {
  showPath: true,
  showClicks: true,
  pathColor: '#3b82f6', // blue-500
  clickColor: '#ef4444', // red-500
  pathWidth: 2,
  clickRadius: 8,
  opacity: 0.8,
};

export const useMouseTrackingStore = create<MouseTrackingState>((set, get) => ({
  trackingData: null,
  visualization: DEFAULT_VISUALIZATION,
  currentTime: 0,
  isPlaying: false,

  actions: {
    setTrackingData: (data: MouseTrackingData) => {
      set({ trackingData: data });
    },

    clearTrackingData: () => {
      set({ trackingData: null, currentTime: 0 });
    },

    updateVisualization: (settings: Partial<MouseTrackingVisualization>) => {
      set(state => ({
        visualization: { ...state.visualization, ...settings }
      }));
    },

    setCurrentTime: (time: number) => {
      set({ currentTime: time });
    },

    setIsPlaying: (playing: boolean) => {
      set({ isPlaying: playing });
    },

    getPointsAtTime: (time: number, tolerance: number = 100) => {
      const { trackingData } = get();
      if (!trackingData) return [];

      return trackingData.points.filter(point => 
        Math.abs(point.timestamp - time) <= tolerance
      );
    },

    getClicksInRange: (startTime: number, endTime: number) => {
      const { trackingData } = get();
      if (!trackingData) return [];

      return trackingData.points.filter(point => 
        point.action === 'click' && 
        point.timestamp >= startTime && 
        point.timestamp <= endTime
      );
    },

    normalizeMouseActions: (actions: MouseAction[], videoWidth: number, videoHeight: number, tabWidth?: number, tabHeight?: number) => {
      // Use tab dimensions if available, otherwise assume video dimensions
      const sourceWidth = tabWidth || videoWidth;
      const sourceHeight = tabHeight || videoHeight;

      let currentTimestamp = 0;
      const baseTimestamp = Date.now();

      return actions.map((action, index) => {
        // If no timestamp provided, estimate based on index (assuming 50ms intervals)
        if (!action.timestamp) {
          currentTimestamp = baseTimestamp + (index * 50);
        } else {
          currentTimestamp = action.timestamp;
        }

        // Normalize coordinates to 0-1 range
        const normalizedX = Math.max(0, Math.min(1, action.coords.x / sourceWidth));
        const normalizedY = Math.max(0, Math.min(1, action.coords.y / sourceHeight));

        return {
          x: action.coords.x,
          y: action.coords.y,
          timestamp: currentTimestamp,
          action: action.action,
          normalizedX,
          normalizedY,
        };
      });
    },
  },
}));

/**
 * Utility functions for mouse tracking data processing
 */
export class MouseTrackingUtils {
  /**
   * Convert extension mouse data to tracking data
   */
  static fromExtensionData(
    mouseActions: MouseAction[],
    videoWidth: number,
    videoHeight: number,
    recordingInfo: MouseTrackingData['recordingInfo'],
    id: string = `tracking_${Date.now()}`
  ): MouseTrackingData {
    const { actions } = useMouseTrackingStore.getState();
    
    const points = actions.normalizeMouseActions(
      mouseActions,
      videoWidth,
      videoHeight,
      recordingInfo.tabInfo?.width,
      recordingInfo.tabInfo?.height
    );

    return {
      id,
      points,
      recordingInfo,
      videoWidth,
      videoHeight,
    };
  }

  /**
   * Get mouse position at specific time with interpolation
   */
  static getInterpolatedPosition(
    points: MouseTrackingPoint[],
    time: number
  ): { x: number; y: number; normalizedX: number; normalizedY: number } | null {
    if (points.length === 0) return null;

    // Find the two points that bracket the target time
    let beforePoint: MouseTrackingPoint | null = null;
    let afterPoint: MouseTrackingPoint | null = null;

    for (let i = 0; i < points.length; i++) {
      const point = points[i];
      
      if (point.timestamp <= time) {
        beforePoint = point;
      } else {
        afterPoint = point;
        break;
      }
    }

    // If we only have one point or time is before all points
    if (!beforePoint) return afterPoint ? { x: afterPoint.x, y: afterPoint.y, normalizedX: afterPoint.normalizedX, normalizedY: afterPoint.normalizedY } : null;
    if (!afterPoint) return { x: beforePoint.x, y: beforePoint.y, normalizedX: beforePoint.normalizedX, normalizedY: beforePoint.normalizedY };

    // Interpolate between the two points
    const timeDiff = afterPoint.timestamp - beforePoint.timestamp;
    const timeRatio = timeDiff > 0 ? (time - beforePoint.timestamp) / timeDiff : 0;

    return {
      x: beforePoint.x + (afterPoint.x - beforePoint.x) * timeRatio,
      y: beforePoint.y + (afterPoint.y - beforePoint.y) * timeRatio,
      normalizedX: beforePoint.normalizedX + (afterPoint.normalizedX - beforePoint.normalizedX) * timeRatio,
      normalizedY: beforePoint.normalizedY + (afterPoint.normalizedY - beforePoint.normalizedY) * timeRatio,
    };
  }

  /**
   * Filter points by time range
   */
  static filterPointsByTimeRange(
    points: MouseTrackingPoint[],
    startTime: number,
    endTime: number
  ): MouseTrackingPoint[] {
    return points.filter(point => 
      point.timestamp >= startTime && point.timestamp <= endTime
    );
  }

  /**
   * Get statistics about mouse tracking data
   */
  static getStatistics(points: MouseTrackingPoint[]) {
    const moves = points.filter(p => p.action === 'move');
    const clicks = points.filter(p => p.action === 'click');
    
    let totalDistance = 0;
    for (let i = 1; i < moves.length; i++) {
      const prev = moves[i - 1];
      const curr = moves[i];
      const dx = curr.x - prev.x;
      const dy = curr.y - prev.y;
      totalDistance += Math.sqrt(dx * dx + dy * dy);
    }

    return {
      totalPoints: points.length,
      totalMoves: moves.length,
      totalClicks: clicks.length,
      totalDistance: Math.round(totalDistance),
      duration: points.length > 0 ? points[points.length - 1].timestamp - points[0].timestamp : 0,
    };
  }
}
