import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useMouseTrackingStore, MouseTrackingUtils } from '../store/use-mouse-tracking-store';
import { Mouse, Eye, EyeOff, Palette, BarChart3, Target } from 'lucide-react';

interface MouseTrackingPanelProps {
  className?: string;
}

export function MouseTrackingPanel({ className }: MouseTrackingPanelProps) {
  const { trackingData, visualization, actions } = useMouseTrackingStore();
  const [showAdvanced, setShowAdvanced] = useState(false);

  if (!trackingData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mouse className="h-5 w-5" />
            Mouse Tracking
          </CardTitle>
          <CardDescription>
            No mouse tracking data available
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  const stats = MouseTrackingUtils.getStatistics(trackingData.points);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mouse className="h-5 w-5" />
          Mouse Tracking
        </CardTitle>
        <CardDescription>
          Visualize and control mouse movement data from screen recording
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Statistics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <div className="text-sm font-medium">Total Points</div>
            <Badge variant="secondary">{stats.totalPoints}</Badge>
          </div>
          <div className="space-y-1">
            <div className="text-sm font-medium">Clicks</div>
            <Badge variant="secondary">{stats.totalClicks}</Badge>
          </div>
          <div className="space-y-1">
            <div className="text-sm font-medium">Distance</div>
            <Badge variant="secondary">{stats.totalDistance}px</Badge>
          </div>
          <div className="space-y-1">
            <div className="text-sm font-medium">Duration</div>
            <Badge variant="secondary">{(stats.duration / 1000).toFixed(1)}s</Badge>
          </div>
        </div>

        <Separator />

        {/* Visibility Controls */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="show-path" className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Show Mouse Path
            </Label>
            <Switch
              id="show-path"
              checked={visualization.showPath}
              onCheckedChange={(checked) => 
                actions.updateVisualization({ showPath: checked })
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <Label htmlFor="show-clicks" className="flex items-center gap-2">
              <Target className="h-4 w-4" />
              Show Clicks
            </Label>
            <Switch
              id="show-clicks"
              checked={visualization.showClicks}
              onCheckedChange={(checked) => 
                actions.updateVisualization({ showClicks: checked })
              }
            />
          </div>
        </div>

        <Separator />

        {/* Style Controls */}
        <div className="space-y-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="w-full"
          >
            <Palette className="h-4 w-4 mr-2" />
            {showAdvanced ? 'Hide' : 'Show'} Style Options
          </Button>

          {showAdvanced && (
            <div className="space-y-4 pt-2">
              {/* Opacity */}
              <div className="space-y-2">
                <Label className="text-sm">Opacity</Label>
                <Slider
                  value={[visualization.opacity * 100]}
                  onValueChange={([value]) => 
                    actions.updateVisualization({ opacity: value / 100 })
                  }
                  max={100}
                  min={10}
                  step={10}
                  className="w-full"
                />
                <div className="text-xs text-muted-foreground text-right">
                  {Math.round(visualization.opacity * 100)}%
                </div>
              </div>

              {/* Path Width */}
              <div className="space-y-2">
                <Label className="text-sm">Path Width</Label>
                <Slider
                  value={[visualization.pathWidth]}
                  onValueChange={([value]) => 
                    actions.updateVisualization({ pathWidth: value })
                  }
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <div className="text-xs text-muted-foreground text-right">
                  {visualization.pathWidth}px
                </div>
              </div>

              {/* Click Radius */}
              <div className="space-y-2">
                <Label className="text-sm">Click Size</Label>
                <Slider
                  value={[visualization.clickRadius]}
                  onValueChange={([value]) => 
                    actions.updateVisualization({ clickRadius: value })
                  }
                  max={20}
                  min={4}
                  step={2}
                  className="w-full"
                />
                <div className="text-xs text-muted-foreground text-right">
                  {visualization.clickRadius}px
                </div>
              </div>

              {/* Color Presets */}
              <div className="space-y-2">
                <Label className="text-sm">Color Theme</Label>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => actions.updateVisualization({
                      pathColor: '#3b82f6',
                      clickColor: '#ef4444'
                    })}
                    className="h-8"
                  >
                    <div className="w-3 h-3 bg-blue-500 rounded-full mr-1" />
                    Blue
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => actions.updateVisualization({
                      pathColor: '#10b981',
                      clickColor: '#f59e0b'
                    })}
                    className="h-8"
                  >
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-1" />
                    Green
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => actions.updateVisualization({
                      pathColor: '#8b5cf6',
                      clickColor: '#ec4899'
                    })}
                    className="h-8"
                  >
                    <div className="w-3 h-3 bg-purple-500 rounded-full mr-1" />
                    Purple
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* Recording Info */}
        {trackingData.recordingInfo.tabInfo && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Recording Source</Label>
            <div className="text-xs text-muted-foreground space-y-1">
              <div className="truncate">
                <strong>Tab:</strong> {trackingData.recordingInfo.tabInfo.title}
              </div>
              <div className="truncate">
                <strong>URL:</strong> {trackingData.recordingInfo.tabInfo.url}
              </div>
              <div>
                <strong>Size:</strong> {trackingData.recordingInfo.tabInfo.width}×{trackingData.recordingInfo.tabInfo.height}
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => actions.clearTrackingData()}
            className="w-full"
          >
            Clear Mouse Data
          </Button>
          <div className="text-xs text-muted-foreground text-center">
            Press Ctrl+M to toggle visibility during playback
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
